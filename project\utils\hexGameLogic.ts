import { Hex<PERSON><PERSON>, <PERSON>Config, HexGameState, <PERSON><PERSON><PERSON><PERSON><PERSON>, GameM<PERSON> } from '@/types/game';

// Axial coordinates, flat-top hexes - 6 neighbors
export const HEX_NEIGHBOR_OFFSETS: [number, number][] = [
  [+1, 0], [0, +1], [-1, +1],
  [-1, 0], [0, -1], [+1, -1]
];

export const HEX_DIFFICULTY_CONFIGS: Record<Difficulty, GameConfig> = {
  easy: {
    size: 6,
    numColors: 4,
    maxMoves: 18, // Reduced due to 6 neighbors vs 4
    timeLimit: 120,
    mode: 'hexagon' as GameMode,
  },
  medium: {
    size: 8,
    numColors: 5,
    maxMoves: 16,
    timeLimit: 90,
    mode: 'hexagon' as GameMode,
  },
  hard: {
    size: 10,
    numColors: 6,
    maxMoves: 14,
    timeLimit: 60,
    mode: 'hexagon' as GameMode,
  },
};

export const COLORS = [
  '#ef4444', // red
  '#3b82f6', // blue
  '#10b981', // green
  '#f59e0b', // yellow
  '#8b5cf6', // purple
  '#ec4899', // pink
];

/**
 * Calculate axial distance between two hex cells
 */
export function axialDistance(a: HexCell, b: HexCell): number {
  const dq = Math.abs(a.q - b.q);
  const dr = Math.abs(a.r - b.r);
  return Math.max(dq, dr, Math.abs(dq + dr));
}

/**
 * Convert axial coordinates to pixel position (flat-top hexes)
 */
export function axialToPixel(q: number, r: number, size: number): { x: number; y: number } {
  const x = size * (3/2 * q);
  const y = size * (Math.sqrt(3)/2 * q + Math.sqrt(3) * r);
  return { x, y };
}

/**
 * Generate hex grid key from coordinates
 */
export function getHexKey(q: number, r: number): string {
  return `${q},${r}`;
}

/**
 * Parse hex grid key to coordinates
 */
export function parseHexKey(key: string): { q: number; r: number } {
  const [q, r] = key.split(',').map(Number);
  return { q, r };
}

/**
 * Flood fill algorithm for hexagonal grid using axial coordinates
 * Based on the Python algorithm provided
 */
export function floodFillHex(
  board: Map<string, HexCell>,
  startQ: number,
  startR: number,
  newColor: number
): number {
  const startKey = getHexKey(startQ, startR);
  const oldColor = board.get(startKey)?.color;

  if (oldColor === undefined || oldColor === newColor) {
    return 0; // nothing to do
  }

  const queue: string[] = [startKey];
  const flooded = new Set<string>([startKey]);

  // Set the start cell to new color immediately
  const startCell = board.get(startKey)!;
  startCell.color = newColor;

  while (queue.length > 0) {
    const currentKey = queue.shift()!;
    const { q, r } = parseHexKey(currentKey);

    for (const [dq, dr] of HEX_NEIGHBOR_OFFSETS) {
      const nq = q + dq;
      const nr = r + dr;
      const neighborKey = getHexKey(nq, nr);

      if (board.has(neighborKey) &&
          !flooded.has(neighborKey) &&
          board.get(neighborKey)!.color === oldColor) {

        board.get(neighborKey)!.color = newColor;
        flooded.add(neighborKey);
        queue.push(neighborKey);
      }
    }
  }

  return flooded.size;
}

export class HexColorFloodGame {
  private config: GameConfig;
  private state: HexGameState;
  private timerInterval?: ReturnType<typeof setInterval>;
  private homoSet: Set<string>; // Track homo region for efficiency

  constructor(difficulty: Difficulty = 'medium') {
    this.config = HEX_DIFFICULTY_CONFIGS[difficulty];
    this.homoSet = new Set();
    this.state = this.createInitialState();
  }

  private createInitialState(): HexGameState {
    const grid = this.initializeBoard();
    const boundary = this.calculateBoundary(grid);

    return {
      grid,
      moveCount: 0,
      isWon: false,
      isGameOver: false,
      timeRemaining: this.config.timeLimit,
      score: 0,
      isPaused: false,
      boundary,
    };
  }

  public initializeBoard(): Map<string, HexCell> {
    const { size, numColors } = this.config;
    const grid = new Map<string, HexCell>();

    // Create rhombus-shaped hex grid
    for (let q = 0; q < size; q++) {
      for (let r = 0; r < size; r++) {
        const key = getHexKey(q, r);
        grid.set(key, {
          q,
          r,
          color: Math.floor(Math.random() * numColors),
          isHomo: false,
        });
      }
    }

    // Set anchor cell (0,0) as homo
    const anchorKey = getHexKey(0, 0);
    const anchorCell = grid.get(anchorKey)!;
    anchorCell.isHomo = true;
    this.homoSet.add(anchorKey);

    // Initial flood propagation from anchor
    this.propagateFloodFromAnchor(grid);

    return grid;
  }

  private propagateFloodFromAnchor(grid: Map<string, HexCell>): void {
    const anchorKey = getHexKey(0, 0);
    const anchorCell = grid.get(anchorKey)!;
    const targetColor = anchorCell.color;

    const queue: string[] = [anchorKey];
    const visited = new Set<string>([anchorKey]);

    // BFS flood fill from anchor
    while (queue.length > 0) {
      const currentKey = queue.shift()!;
      const { q, r } = parseHexKey(currentKey);

      for (const [dq, dr] of HEX_NEIGHBOR_OFFSETS) {
        const nq = q + dq;
        const nr = r + dr;
        const neighborKey = getHexKey(nq, nr);

        if (grid.has(neighborKey) &&
            !visited.has(neighborKey) &&
            grid.get(neighborKey)!.color === targetColor) {

          const neighbor = grid.get(neighborKey)!;
          neighbor.isHomo = true;
          this.homoSet.add(neighborKey);
          queue.push(neighborKey);
          visited.add(neighborKey);
        }
      }
    }
  }

  private calculateBoundary(grid: Map<string, HexCell>): string[] {
    const boundary: string[] = [];

    for (const cellKey of this.homoSet) {
      const { q, r } = parseHexKey(cellKey);
      let hasHeteroNeighbor = false;

      for (const [dq, dr] of HEX_NEIGHBOR_OFFSETS) {
        const nq = q + dq;
        const nr = r + dr;
        const neighborKey = getHexKey(nq, nr);

        if (grid.has(neighborKey) && !this.homoSet.has(neighborKey)) {
          hasHeteroNeighbor = true;
          break;
        }
      }

      if (hasHeteroNeighbor) {
        boundary.push(cellKey);
      }
    }

    return boundary;
  }

  /**
   * Apply player move using the boundary-based approach from the Python algorithm
   */
  private applyPlayerMove(board: Map<string, HexCell>, newColor: number): void {
    // 1. Recolor entire homo region
    for (const cellKey of this.homoSet) {
      board.get(cellKey)!.color = newColor;
    }

    // 2. Grow the region: check boundary neighbors only
    const queue: string[] = [...this.state.boundary];

    while (queue.length > 0) {
      const cellKey = queue.shift()!;
      const { q, r } = parseHexKey(cellKey);

      for (const [dq, dr] of HEX_NEIGHBOR_OFFSETS) {
        const nq = q + dq;
        const nr = r + dr;
        const neighborKey = getHexKey(nq, nr);

        if (board.has(neighborKey) && !this.homoSet.has(neighborKey)) {
          const neighbor = board.get(neighborKey)!;
          if (neighbor.color === newColor) {
            neighbor.isHomo = true;
            this.homoSet.add(neighborKey);
            queue.push(neighborKey);
          }
        }
      }
    }

    // 3. Recompute the new boundary
    this.state.boundary = this.calculateBoundary(board);
  }

  public handlePlayerTap(tappedCell: HexCell): boolean {
    if (this.state.isWon || this.state.isGameOver || this.state.isPaused) {
      return false;
    }

    const newColor = tappedCell.color;
    const anchorKey = getHexKey(0, 0);
    const anchorCell = this.state.grid.get(anchorKey)!;
    const oldColor = anchorCell.color;

    if (newColor === oldColor) {
      return false; // No change needed
    }

    // Increment move count
    this.state.moveCount++;

    // Apply player move using boundary-based approach
    this.applyPlayerMove(this.state.grid, newColor);

    // Check win condition
    this.checkWinCondition();

    // Check game over condition
    if (this.state.moveCount >= this.config.maxMoves && !this.state.isWon) {
      this.state.isGameOver = true;
      this.stopTimer();
    }

    return true;
  }

  /**
   * Get neighbor cell keys for a hex cell using axial coordinates
   */
  private getHexNeighbors(q: number, r: number): string[] {
    const neighbors: string[] = [];

    for (const [dq, dr] of HEX_NEIGHBOR_OFFSETS) {
      const nq = q + dq;
      const nr = r + dr;
      const neighborKey = getHexKey(nq, nr);

      if (this.state.grid.has(neighborKey)) {
        neighbors.push(neighborKey);
      }
    }

    return neighbors;
  }

  private checkWinCondition(): void {
    const totalCells = this.state.grid.size;
    let homoCount = 0;

    for (const [, cell] of this.state.grid) {
      if (cell.isHomo) {
        homoCount++;
      }
    }

    if (homoCount === totalCells) {
      this.state.isWon = true;
      this.calculateScore();
      this.stopTimer();
    }
  }

  private calculateScore(): void {
    const movesBonus = Math.max(0, this.config.maxMoves - this.state.moveCount) * 100;
    const timeBonus = this.state.timeRemaining * 10;
    const difficultyMultiplier = this.config.size / 6; // Easy: 1x, Medium: 1.33x, Hard: 1.67x
    
    this.state.score = Math.round((movesBonus + timeBonus) * difficultyMultiplier);
  }

  public startTimer(onTick?: (timeRemaining: number) => void): void {
    this.stopTimer();
    this.timerInterval = setInterval(() => {
      if (!this.state.isPaused && this.state.timeRemaining > 0) {
        this.state.timeRemaining--;
        onTick?.(this.state.timeRemaining);

        if (this.state.timeRemaining === 0 && !this.state.isWon) {
          this.state.isGameOver = true;
          this.stopTimer();
        }
      }
    }, 1000);
  }

  public stopTimer(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = undefined;
    }
  }

  public pauseGame(): void {
    this.state.isPaused = true;
  }

  public resumeGame(): void {
    this.state.isPaused = false;
  }

  public resetGame(difficulty?: Difficulty): void {
    this.stopTimer();
    if (difficulty) {
      this.config = HEX_DIFFICULTY_CONFIGS[difficulty];
    }
    this.state = this.createInitialState();
  }

  public getState(): HexGameState {
    return { ...this.state, grid: new Map(this.state.grid) };
  }

  public getConfig(): GameConfig {
    return { ...this.config };
  }

  public getProgress(): number {
    const totalCells = this.state.grid.size;
    let homoCount = 0;

    for (const [, cell] of this.state.grid) {
      if (cell.isHomo) {
        homoCount++;
      }
    }

    return homoCount / totalCells;
  }

  public getGridAsArray(): HexCell[] {
    return Array.from(this.state.grid.values());
  }
}
