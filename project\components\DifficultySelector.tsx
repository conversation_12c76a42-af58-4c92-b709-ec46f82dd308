import React, { useState } from 'react';
import {
  View,
  Text,
  Pressable,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Zap, Target, Clock, Square, Hexagon } from 'lucide-react-native';
import { Difficulty, GameMode } from '@/types/game';
import { DIFFICULTY_CONFIGS } from '@/utils/gameLogic';
import { HEX_DIFFICULTY_CONFIGS } from '@/utils/hexGameLogic';

interface DifficultySelectorProps {
  onDifficultySelect: (difficulty: Difficulty, mode: GameMode) => void;
  currentDifficulty: Difficulty;
}

export default function DifficultySelector({
  onDifficultySelect,
  currentDifficulty,
}: DifficultySelectorProps) {
  const [selectedMode, setSelectedMode] = useState<GameMode>('square');

  const difficultyInfo = {
    easy: {
      title: 'Easy',
      description: 'Perfect for beginners',
      icon: Target,
      colors: ['#10b981', '#065f46'] as const,
    },
    medium: {
      title: 'Medium',
      description: 'Balanced challenge',
      icon: Zap,
      colors: ['#f59e0b', '#92400e'] as const,
    },
    hard: {
      title: 'Hard',
      description: 'For color flood masters',
      icon: Clock,
      colors: ['#ef4444', '#991b1b'] as const,
    },
  };

  const modeInfo = {
    square: {
      title: 'Square Grid',
      description: '4-neighbor classic mode',
      icon: Square,
      colors: ['#3b82f6', '#1d4ed8'] as const,
    },
    hexagon: {
      title: 'Hexagon Grid',
      description: '6-neighbor challenge',
      icon: Hexagon,
      colors: ['#8b5cf6', '#6d28d9'] as const,
    },
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return mins > 0 ? `${mins}:${secs.toString().padStart(2, '0')}` : `${secs}s`;
  };

  const getCurrentConfig = (difficulty: Difficulty) => {
    return selectedMode === 'hexagon' ? HEX_DIFFICULTY_CONFIGS[difficulty] : DIFFICULTY_CONFIGS[difficulty];
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#0f172a', '#1e293b', '#334155']}
        style={styles.background}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Color Flood</Text>
          <Text style={styles.subtitle}>
            Choose your game mode and difficulty level!
          </Text>
        </View>

        {/* Mode Selection */}
        <View style={styles.modeContainer}>
          <Text style={styles.sectionTitle}>Game Mode</Text>
          <View style={styles.modeButtons}>
            {(Object.keys(modeInfo) as GameMode[]).map((mode) => {
              const info = modeInfo[mode];
              const IconComponent = info.icon;
              const isSelected = mode === selectedMode;

              return (
                <Pressable
                  key={mode}
                  style={({ pressed }) => [
                    styles.modeButton,
                    pressed && styles.modeButtonPressed,
                    isSelected && styles.selectedModeButton,
                  ]}
                  onPress={() => setSelectedMode(mode)}
                >
                  <LinearGradient
                    colors={isSelected ? info.colors : ['#374151', '#4b5563'] as const}
                    style={styles.modeButtonGradient}
                  >
                    <IconComponent size={24} color="#ffffff" />
                    <Text style={styles.modeButtonTitle}>{info.title}</Text>
                    <Text style={styles.modeButtonDescription}>{info.description}</Text>
                  </LinearGradient>
                </Pressable>
              );
            })}
          </View>
        </View>

        <View style={styles.difficultyContainer}>
          <Text style={styles.sectionTitle}>Difficulty</Text>
          {(Object.keys(difficultyInfo) as Difficulty[]).map((difficulty) => {
            const info = difficultyInfo[difficulty];
            const config = getCurrentConfig(difficulty);
            const IconComponent = info.icon;
            const isSelected = difficulty === currentDifficulty;

            return (
              <Pressable
                key={difficulty}
                style={({ pressed }) => [
                  styles.difficultyCard,
                  pressed && styles.cardPressed,
                  isSelected && styles.selectedCard,
                ]}
                onPress={() => onDifficultySelect(difficulty, selectedMode)}
              >
                <LinearGradient
                  colors={info.colors}
                  style={styles.cardGradient}
                >
                  <View style={styles.cardHeader}>
                    <IconComponent size={32} color="#ffffff" />
                    <Text style={styles.difficultyTitle}>{info.title}</Text>
                  </View>
                  
                  <Text style={styles.difficultyDescription}>
                    {info.description}
                  </Text>

                  <View style={styles.statsContainer}>
                    <View style={styles.statItem}>
                      <Text style={styles.statLabel}>Grid Size</Text>
                      <Text style={styles.statValue}>{config.size}×{config.size}</Text>
                    </View>
                    <View style={styles.statItem}>
                      <Text style={styles.statLabel}>Max Moves</Text>
                      <Text style={styles.statValue}>{config.maxMoves}</Text>
                    </View>
                    <View style={styles.statItem}>
                      <Text style={styles.statLabel}>Time Limit</Text>
                      <Text style={styles.statValue}>{formatTime(config.timeLimit)}</Text>
                    </View>
                  </View>
                </LinearGradient>
              </Pressable>
            );
          })}
        </View>

        <View style={styles.instructions}>
          <Text style={styles.instructionsTitle}>How to Play</Text>
          <Text style={styles.instructionsText}>
            • Tap any colored cell to flood the board with that color{'\n'}
            • Start from the top-left corner and expand your flooded region{'\n'}
            • Complete the board within the move and time limits{'\n'}
            • Higher scores for fewer moves and faster completion
          </Text>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 32,
  },
  title: {
    fontSize: 36,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#94a3b8',
    textAlign: 'center',
    lineHeight: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginBottom: 16,
    textAlign: 'center',
  },
  modeContainer: {
    marginBottom: 32,
  },
  modeButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modeButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  modeButtonPressed: {
    opacity: 0.9,
  },
  selectedModeButton: {
    shadowColor: '#8b5cf6',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.5,
    shadowRadius: 4,
    elevation: 4,
  },
  modeButtonGradient: {
    padding: 16,
    alignItems: 'center',
  },
  modeButtonTitle: {
    fontSize: 14,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginTop: 8,
    textAlign: 'center',
  },
  modeButtonDescription: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
    marginTop: 4,
    textAlign: 'center',
  },
  difficultyContainer: {
    gap: 16,
    marginBottom: 32,
  },
  difficultyCard: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  selectedCard: {
    shadowColor: '#3b82f6',
    shadowOpacity: 0.5,
  },
  cardPressed: {
    opacity: 0.9,
  },
  cardGradient: {
    padding: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 12,
  },
  difficultyTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
  },
  difficultyDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
  },
  instructions: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 32,
  },
  instructionsTitle: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginBottom: 12,
  },
  instructionsText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#e2e8f0',
    lineHeight: 20,
  },
});