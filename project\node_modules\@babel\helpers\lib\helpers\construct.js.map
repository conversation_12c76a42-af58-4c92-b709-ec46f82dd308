{"version": 3, "names": ["_isNativeReflectConstruct", "require", "_setPrototypeOf", "_construct", "Parent", "args", "Class", "isNativeReflectConstruct", "Reflect", "construct", "apply", "arguments", "a", "push", "instance", "bind", "setPrototypeOf", "prototype"], "sources": ["../../src/helpers/construct.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.ts\";\nimport setPrototypeOf from \"./setPrototypeOf.ts\";\n\nexport default function _construct(\n  Parent: Function,\n  args: any[],\n  Class: Function,\n): any {\n  if (isNativeReflectConstruct()) {\n    // Avoid issues with Class being present but undefined when it wasn't\n    // present in the original call.\n    return Reflect.construct.apply(null, arguments as any);\n  }\n  // NOTE: If Parent !== Class, the correct __proto__ is set *after*\n  //       calling the constructor.\n  var a: [any, ...any[]] = [null];\n  a.push.apply(a, args);\n  var instance = new (Parent.bind.apply(Parent, a))();\n  if (Class) setPrototypeOf(instance, Class.prototype);\n  return instance;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,yBAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AAEe,SAASE,UAAUA,CAChCC,MAAgB,EAChBC,IAAW,EACXC,KAAe,EACV;EACL,IAAI,IAAAC,iCAAwB,EAAC,CAAC,EAAE;IAG9B,OAAOC,OAAO,CAACC,SAAS,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAgB,CAAC;EACxD;EAGA,IAAIC,CAAkB,GAAG,CAAC,IAAI,CAAC;EAC/BA,CAAC,CAACC,IAAI,CAACH,KAAK,CAACE,CAAC,EAAEP,IAAI,CAAC;EACrB,IAAIS,QAAQ,GAAG,KAAKV,MAAM,CAACW,IAAI,CAACL,KAAK,CAACN,MAAM,EAAEQ,CAAC,CAAC,EAAE,CAAC;EACnD,IAAIN,KAAK,EAAE,IAAAU,uBAAc,EAACF,QAAQ,EAAER,KAAK,CAACW,SAAS,CAAC;EACpD,OAAOH,QAAQ;AACjB", "ignoreList": []}