import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { HexCell } from '@/types/game';
import { COLORS } from '@/utils/hexGameLogic';
import { axialToPixel } from '@/utils/hexGameLogic';
import HexGameCell from './HexGameCell';

interface HexGameBoardProps {
  grid: Map<string, HexCell>;
  gridSize: number;
  onCellPress: (cell: HexCell) => void;
  disabled?: boolean;
}

const { width: screenWidth } = Dimensions.get('window');
const BOARD_PADDING = 32;
const BOARD_SIZE = screenWidth - BOARD_PADDING;

export default function HexGameBoard({ 
  grid, 
  gridSize, 
  onCellPress, 
  disabled = false 
}: HexGameBoardProps) {
  // Calculate hex size based on grid size and available space
  const hexSize = Math.min(BOARD_SIZE / (gridSize * 1.5), 60); // Max 60px per hex
  
  // Calculate board dimensions based on hex layout
  const boardWidth = gridSize * hexSize * 1.5;
  const boardHeight = gridSize * hexSize * Math.sqrt(3);
  
  // Center the board
  const boardOffsetX = (BOARD_SIZE - boardWidth) / 2;
  const boardOffsetY = (BOARD_SIZE - boardHeight) / 2;

  const renderHexCell = (cell: HexCell) => {
    const { x, y } = axialToPixel(cell.q, cell.r, hexSize);
    
    return (
      <HexGameCell
        key={`${cell.q}-${cell.r}`}
        cell={cell}
        size={hexSize}
        color={COLORS[cell.color]}
        onPress={() => !disabled && onCellPress(cell)}
        disabled={disabled}
        style={{
          position: 'absolute',
          left: x + boardOffsetX,
          top: y + boardOffsetY,
        }}
      />
    );
  };

  return (
    <View style={[styles.container, { width: BOARD_SIZE, height: BOARD_SIZE }]}>
      {Array.from(grid.values()).map(renderHexCell)}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
