import React from 'react';
import { Pressable, StyleSheet, ViewStyle } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming,
  withSequence,
  withSpring,
} from 'react-native-reanimated';
import Svg, { Polygon } from 'react-native-svg';
import { HexCell } from '@/types/game';

interface HexGameCellProps {
  cell: HexCell;
  size: number;
  color: string;
  onPress: () => void;
  disabled?: boolean;
  style?: ViewStyle;
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export default function HexGameCell({ 
  cell, 
  size, 
  color, 
  onPress, 
  disabled = false,
  style 
}: HexGameCellProps) {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(cell.isHomo ? 0.8 : 1);

  // Calculate hexagon points for flat-top orientation
  const getHexagonPoints = (centerX: number, centerY: number, radius: number): string => {
    const points: [number, number][] = [];
    for (let i = 0; i < 6; i++) {
      const angle = (Math.PI / 3) * i; // 60 degrees in radians
      const x = centerX + radius * Math.cos(angle);
      const y = centerY + radius * Math.sin(angle);
      points.push([x, y]);
    }
    return points.map(([x, y]) => `${x},${y}`).join(' ');
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.9, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    onPress();
  };

  React.useEffect(() => {
    opacity.value = withTiming(cell.isHomo ? 0.8 : 1, { duration: 200 });
  }, [cell.isHomo]);

  const hexRadius = size * 0.45; // Slightly smaller than container to avoid overlap
  const centerX = size / 2;
  const centerY = size / 2;
  const hexPoints = getHexagonPoints(centerX, centerY, hexRadius);

  return (
    <AnimatedPressable
      style={[
        styles.container,
        {
          width: size,
          height: size,
        },
        animatedStyle,
        style,
      ]}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={handlePress}
      disabled={disabled}
    >
      <Svg width={size} height={size} style={styles.svg}>
        <Polygon
          points={hexPoints}
          fill={color}
          stroke="#ffffff"
          strokeWidth="2"
        />
      </Svg>
      
      {cell.isHomo && (
        <Animated.View 
          style={[
            styles.homoIndicator,
            { 
              backgroundColor: 'rgba(255, 255, 255, 0.4)',
            }
          ]} 
        />
      )}
    </AnimatedPressable>
  );
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  svg: {
    position: 'absolute',
  },
  homoIndicator: {
    position: 'absolute',
    top: '20%',
    right: '20%',
    width: 8,
    height: 8,
    borderRadius: 4,
    zIndex: 1,
  },
});
