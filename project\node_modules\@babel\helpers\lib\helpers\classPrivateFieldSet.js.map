{"version": 3, "names": ["_classApplyDescriptorSet", "require", "_classPrivateFieldGet", "_classPrivateFieldSet", "receiver", "privateMap", "value", "descriptor", "classPrivateFieldGet2", "classApplyDescriptorSet"], "sources": ["../../src/helpers/classPrivateFieldSet.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n/* @onlyBabel7 */\n\nimport classApplyDescriptorSet from \"classApplyDescriptorSet\";\nimport classPrivateFieldGet2 from \"classPrivateFieldGet2\";\nexport default function _classPrivateFieldSet(receiver, privateMap, value) {\n  var descriptor = classPrivateFieldGet2(privateMap, receiver);\n  classApplyDescriptorSet(receiver, descriptor, value);\n  return value;\n}\n"], "mappings": ";;;;;;AAGA,IAAAA,wBAAA,GAAAC,OAAA;AACA,IAAAC,qBAAA,GAAAD,OAAA;AACe,SAASE,qBAAqBA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAE;EACzE,IAAIC,UAAU,GAAGC,qBAAqB,CAACH,UAAU,EAAED,QAAQ,CAAC;EAC5DK,wBAAuB,CAACL,QAAQ,EAAEG,UAAU,EAAED,KAAK,CAAC;EACpD,OAAOA,KAAK;AACd", "ignoreList": []}