// Simple test file to verify hexagon game logic
// Run with: node test-hex-logic.js

// Mock the types and imports for Node.js testing
const HEX_NEIGHBOR_OFFSETS = [
  [+1, 0], [0, +1], [-1, +1],
  [-1, 0], [0, -1], [+1, -1]
];

const HEX_DIFFICULTY_CONFIGS = {
  easy: {
    size: 6,
    numColors: 4,
    maxMoves: 18,
    timeLimit: 120,
    mode: 'hexagon',
  },
  medium: {
    size: 8,
    numColors: 5,
    maxMoves: 16,
    timeLimit: 90,
    mode: 'hexagon',
  },
  hard: {
    size: 10,
    numColors: 6,
    maxMoves: 14,
    timeLimit: 60,
    mode: 'hexagon',
  },
};

function getHexKey(q, r) {
  return `${q},${r}`;
}

function parseHexKey(key) {
  const [q, r] = key.split(',').map(Number);
  return { q, r };
}

// Simplified HexColorFloodGame class for testing
class HexColorFloodGame {
  constructor(difficulty = 'medium') {
    this.config = HEX_DIFFICULTY_CONFIGS[difficulty];
    this.homoSet = new Set();
    this.state = this.createInitialState();
  }

  createInitialState() {
    const grid = this.initializeBoard();
    const boundary = this.calculateBoundary(grid);

    return {
      grid,
      moveCount: 0,
      isWon: false,
      isGameOver: false,
      timeRemaining: this.config.timeLimit,
      score: 0,
      isPaused: false,
      boundary,
    };
  }

  initializeBoard() {
    const { size, numColors } = this.config;
    const grid = new Map();

    // Create rhombus-shaped hex grid
    for (let q = 0; q < size; q++) {
      for (let r = 0; r < size; r++) {
        const key = getHexKey(q, r);
        grid.set(key, {
          q,
          r,
          color: Math.floor(Math.random() * numColors),
          isHomo: false,
        });
      }
    }

    // Set anchor cell (0,0) as homo
    const anchorKey = getHexKey(0, 0);
    const anchorCell = grid.get(anchorKey);
    anchorCell.isHomo = true;
    this.homoSet.add(anchorKey);

    // Initial flood propagation from anchor
    this.propagateFloodFromAnchor(grid);

    return grid;
  }

  propagateFloodFromAnchor(grid) {
    const anchorKey = getHexKey(0, 0);
    const anchorCell = grid.get(anchorKey);
    const targetColor = anchorCell.color;

    const queue = [anchorKey];
    const visited = new Set([anchorKey]);

    // BFS flood fill from anchor
    while (queue.length > 0) {
      const currentKey = queue.shift();
      const { q, r } = parseHexKey(currentKey);

      for (const [dq, dr] of HEX_NEIGHBOR_OFFSETS) {
        const nq = q + dq;
        const nr = r + dr;
        const neighborKey = getHexKey(nq, nr);

        if (grid.has(neighborKey) &&
            !visited.has(neighborKey) &&
            grid.get(neighborKey).color === targetColor) {

          const neighbor = grid.get(neighborKey);
          neighbor.isHomo = true;
          this.homoSet.add(neighborKey);
          queue.push(neighborKey);
          visited.add(neighborKey);
        }
      }
    }
  }

  calculateBoundary(grid) {
    const boundary = [];

    for (const cellKey of this.homoSet) {
      const { q, r } = parseHexKey(cellKey);
      let hasHeteroNeighbor = false;

      for (const [dq, dr] of HEX_NEIGHBOR_OFFSETS) {
        const nq = q + dq;
        const nr = r + dr;
        const neighborKey = getHexKey(nq, nr);

        if (grid.has(neighborKey) && !this.homoSet.has(neighborKey)) {
          hasHeteroNeighbor = true;
          break;
        }
      }

      if (hasHeteroNeighbor) {
        boundary.push(cellKey);
      }
    }

    return boundary;
  }

  applyPlayerMove(board, newColor) {
    // 1. Recolor entire homo region
    for (const cellKey of this.homoSet) {
      board.get(cellKey).color = newColor;
    }

    // 2. Grow the region: check boundary neighbors only
    const queue = [...this.state.boundary];

    while (queue.length > 0) {
      const cellKey = queue.shift();
      const { q, r } = parseHexKey(cellKey);

      for (const [dq, dr] of HEX_NEIGHBOR_OFFSETS) {
        const nq = q + dq;
        const nr = r + dr;
        const neighborKey = getHexKey(nq, nr);

        if (board.has(neighborKey) && !this.homoSet.has(neighborKey)) {
          const neighbor = board.get(neighborKey);
          if (neighbor.color === newColor) {
            neighbor.isHomo = true;
            this.homoSet.add(neighborKey);
            queue.push(neighborKey);
          }
        }
      }
    }

    // 3. Recompute the new boundary
    this.state.boundary = this.calculateBoundary(board);
  }

  handlePlayerTap(tappedCell) {
    if (this.state.isWon || this.state.isGameOver || this.state.isPaused) {
      return false;
    }

    const newColor = tappedCell.color;
    const anchorKey = getHexKey(0, 0);
    const anchorCell = this.state.grid.get(anchorKey);
    const oldColor = anchorCell.color;

    if (newColor === oldColor) {
      return false; // No change needed
    }

    // Increment move count
    this.state.moveCount++;

    // Apply player move using boundary-based approach
    this.applyPlayerMove(this.state.grid, newColor);

    return true;
  }

  getProgress() {
    const totalCells = this.state.grid.size;
    let homoCount = 0;

    for (const [, cell] of this.state.grid) {
      if (cell.isHomo) {
        homoCount++;
      }
    }

    return homoCount / totalCells;
  }
}

// Test functions
function testHexGameInitialization() {
  console.log('Testing hex game initialization...');
  
  const game = new HexColorFloodGame('easy');
  const state = game.state;
  
  console.log(`Grid size: ${state.grid.size} (expected: ${6 * 6})`);
  console.log(`Initial homo cells: ${game.homoSet.size}`);
  console.log(`Initial boundary cells: ${state.boundary.length}`);
  console.log(`Initial progress: ${(game.getProgress() * 100).toFixed(1)}%`);
  
  // Verify anchor cell is homo
  const anchorCell = state.grid.get('0,0');
  console.log(`Anchor cell is homo: ${anchorCell.isHomo}`);
  
  return game;
}

function testPlayerMove(game) {
  console.log('\nTesting player move...');
  
  const initialProgress = game.getProgress();
  const initialMoveCount = game.state.moveCount;
  
  // Find a non-homo cell to tap
  let testCell = null;
  for (const [, cell] of game.state.grid) {
    if (!cell.isHomo) {
      testCell = cell;
      break;
    }
  }
  
  if (testCell) {
    console.log(`Tapping cell at (${testCell.q}, ${testCell.r}) with color ${testCell.color}`);
    const moveResult = game.handlePlayerTap(testCell);
    
    console.log(`Move successful: ${moveResult}`);
    console.log(`Move count: ${game.state.moveCount} (was ${initialMoveCount})`);
    console.log(`Progress: ${(game.getProgress() * 100).toFixed(1)}% (was ${(initialProgress * 100).toFixed(1)}%)`);
    console.log(`Homo cells: ${game.homoSet.size}`);
    console.log(`Boundary cells: ${game.state.boundary.length}`);
  } else {
    console.log('No non-homo cells found to test with');
  }
}

function testBoundaryCalculation(game) {
  console.log('\nTesting boundary calculation...');
  
  const boundary = game.state.boundary;
  console.log(`Boundary has ${boundary.length} cells`);
  
  // Verify each boundary cell is actually on the boundary
  let validBoundary = 0;
  for (const cellKey of boundary) {
    const { q, r } = parseHexKey(cellKey);
    let hasHeteroNeighbor = false;
    
    for (const [dq, dr] of HEX_NEIGHBOR_OFFSETS) {
      const nq = q + dq;
      const nr = r + dr;
      const neighborKey = getHexKey(nq, nr);
      
      if (game.state.grid.has(neighborKey) && !game.homoSet.has(neighborKey)) {
        hasHeteroNeighbor = true;
        break;
      }
    }
    
    if (hasHeteroNeighbor) {
      validBoundary++;
    }
  }
  
  console.log(`Valid boundary cells: ${validBoundary}/${boundary.length}`);
}

// Run tests
console.log('=== Hexagon Game Logic Tests ===\n');

try {
  const game = testHexGameInitialization();
  testBoundaryCalculation(game);
  testPlayerMove(game);
  testBoundaryCalculation(game);
  
  console.log('\n=== All tests completed successfully! ===');
} catch (error) {
  console.error('Test failed:', error);
}
